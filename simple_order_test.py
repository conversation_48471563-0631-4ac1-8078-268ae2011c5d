#!/usr/bin/env python3
"""
简化的下单测试脚本
"""

import asyncio
import websockets
import json
import ssl
import requests
from urllib3.exceptions import InsecureRequestWarning

# 禁用SSL警告
requests.packages.urllib3.disable_warnings(InsecureRequestWarning)

# 配置
WS_URL = "wss://127.0.0.1:8082"
HTTP_BASE_URL = "https://127.0.0.1:8083"

async def test_order():
    """测试下单功能"""
    print("🚀 开始测试下单功能")
    
    # 1. 检查初始余额
    print("\n💰 检查初始余额...")
    response = requests.get(f"{HTTP_BASE_URL}/fapi/v2/balance", verify=False)
    if response.status_code == 200:
        initial_balance = response.json()
        print("初始余额:", json.dumps(initial_balance, indent=2))
    else:
        print(f"❌ 获取余额失败: {response.status_code}")
        return
    
    # 2. 连接WebSocket并下单
    print("\n📝 连接WebSocket并下单...")
    ssl_context = ssl.create_default_context()
    ssl_context.check_hostname = False
    ssl_context.verify_mode = ssl.CERT_NONE
    
    try:
        async with websockets.connect(WS_URL, ssl=ssl_context) as websocket:
            print("✅ WebSocket连接成功")
            
            # 订阅订单更新
            subscribe_msg = {
                "method": "SUBSCRIBE",
                "params": ["test_listen_key"],
                "id": 1
            }
            await websocket.send(json.dumps(subscribe_msg))
            response = await websocket.recv()
            print("📥 订阅响应:", response)
            
            # 发送下单消息
            order_msg = {
                "id": 123,
                "method": "order.place",
                "params": {
                    "symbol": "BTCUSDT",
                    "side": "BUY",
                    "type": "MARKET",
                    "quantity": "0.001",
                    "newClientOrderId": "test_order_001"
                }
            }
            
            print("📤 发送下单消息:", json.dumps(order_msg, indent=2))
            await websocket.send(json.dumps(order_msg))
            
            # 监听响应
            print("🎧 监听响应...")
            for i in range(5):  # 监听5条消息
                try:
                    message = await asyncio.wait_for(websocket.recv(), timeout=2.0)
                    print(f"📥 消息 #{i+1}:", message)
                except asyncio.TimeoutError:
                    print("⏰ 超时")
                    break
                    
    except Exception as e:
        print(f"❌ WebSocket错误: {e}")
    
    # 3. 检查最终余额
    print("\n💰 检查最终余额...")
    response = requests.get(f"{HTTP_BASE_URL}/fapi/v2/balance", verify=False)
    if response.status_code == 200:
        final_balance = response.json()
        print("最终余额:", json.dumps(final_balance, indent=2))
        
        # 比较余额变化
        initial_usdt = float(initial_balance[0]['balance'])
        final_usdt = float(final_balance[0]['balance'])
        print(f"\n📊 余额变化: {initial_usdt} -> {final_usdt} (变化: {final_usdt - initial_usdt})")
    else:
        print(f"❌ 获取最终余额失败: {response.status_code}")
    
    print("\n✅ 测试完成")

if __name__ == "__main__":
    asyncio.run(test_order())
