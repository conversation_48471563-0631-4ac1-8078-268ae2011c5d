#!/bin/bash

echo "🚀 手动订单执行测试"
echo "=" 
echo ""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 测试函数
test_api() {
    local endpoint=$1
    local description=$2
    echo -e "${BLUE}📊 测试 $description...${NC}"
    echo "GET $endpoint"
    local result=$(curl -k -s "https://127.0.0.1:8083$endpoint")
    echo "$result" | jq '.' 2>/dev/null || echo "$result"
    echo ""
    return 0
}

# 格式化显示账户信息
show_account_info() {
    local label=$1
    echo -e "${YELLOW}📊 账户状态 [$label]${NC}"
    echo "-" 
    
    # 获取余额信息
    echo -e "${BLUE}💰 余额信息:${NC}"
    local balance_result=$(curl -k -s "https://127.0.0.1:8083/fapi/v2/balance")
    echo "$balance_result" | jq '.' 2>/dev/null || echo "$balance_result"
    
    echo ""
    echo -e "${BLUE}📈 账户详情:${NC}"
    local account_result=$(curl -k -s "https://127.0.0.1:8083/fapi/v1/account")
    
    # 提取关键信息
    if command -v jq >/dev/null 2>&1; then
        local total_balance=$(echo "$account_result" | jq -r '.totalWalletBalance // "N/A"')
        local available_balance=$(echo "$account_result" | jq -r '.availableBalance // "N/A"')
        local unrealized_pnl=$(echo "$account_result" | jq -r '.totalUnrealizedProfit // "N/A"')
        local positions=$(echo "$account_result" | jq -r '.positions | length')
        
        echo "  总余额: $total_balance USDT"
        echo "  可用余额: $available_balance USDT"
        echo "  未实现盈亏: $unrealized_pnl USDT"
        echo "  持仓数量: $positions"
        
        # 显示活跃持仓
        local active_positions=$(echo "$account_result" | jq -r '.positions[] | select(.positionAmt != "0") | "\(.symbol): \(.positionAmt) @ \(.entryPrice) (盈亏: \(.unrealizedProfit))"')
        if [ -n "$active_positions" ]; then
            echo -e "${GREEN}  活跃持仓:${NC}"
            echo "$active_positions" | while read line; do
                echo "    $line"
            done
        else
            echo "  无活跃持仓"
        fi
    else
        echo "$account_result"
    fi
    echo ""
}

# 主测试流程
main() {
    echo -e "${GREEN}🎯 开始订单执行验证测试${NC}"
    echo ""
    
    # 1. 显示初始状态
    show_account_info "初始状态"
    
    # 2. 显示当前市场数据状态
    echo -e "${YELLOW}📊 系统状态检查${NC}"
    echo "-" 
    
    # 检查数据流状态
    echo -e "${BLUE}🔄 数据流状态:${NC}"
    curl -k -s "https://127.0.0.1:8083/api/v1/datastream/status" | jq '.' 2>/dev/null || curl -k -s "https://127.0.0.1:8083/api/v1/datastream/status"
    echo ""
    
    # 检查系统统计
    echo -e "${BLUE}📈 系统统计:${NC}"
    curl -k -s "https://127.0.0.1:8083/api/v1/stats" | jq '.' 2>/dev/null || curl -k -s "https://127.0.0.1:8083/api/v1/stats"
    echo ""
    
    # 3. 说明下单方法
    echo -e "${YELLOW}📝 下单测试说明${NC}"
    echo "-" 
    echo "由于需要WebSocket连接来下单，请使用以下方法之一进行测试："
    echo ""
    
    echo -e "${GREEN}方法1: 使用Python脚本 (推荐)${NC}"
    echo "1. 安装依赖: pip3 install websockets requests"
    echo "2. 运行测试: python3 quick_order_test.py"
    echo ""
    
    echo -e "${GREEN}方法2: 使用WebSocket工具${NC}"
    echo "1. 连接: wss://127.0.0.1:8082"
    echo "2. 订阅订单更新:"
    echo '   {"method":"SUBSCRIBE","params":["test_listen_key"],"id":1}'
    echo "3. 下单示例:"
    echo '   {"id":123,"method":"order.place","params":{"symbol":"BTCUSDT","side":"BUY","type":"MARKET","quantity":"0.001"}}'
    echo ""
    
    echo -e "${GREEN}方法3: 手动验证流程${NC}"
    echo "1. 记录当前账户状态"
    echo "2. 通过WebSocket下单"
    echo "3. 观察订单执行日志"
    echo "4. 验证账户状态变化"
    echo ""
    
    # 4. 提供验证检查点
    echo -e "${YELLOW}🔍 验证检查点${NC}"
    echo "-" 
    echo "下单后请验证以下要点："
    echo ""
    echo -e "${BLUE}1. BBO价格成交验证:${NC}"
    echo "   - 检查订单执行价格是否接近当前市场价格"
    echo "   - 买单价格应接近ask价格，卖单价格应接近bid价格"
    echo ""
    echo -e "${BLUE}2. 手续费计算验证:${NC}"
    echo "   - 手续费 = 成交金额 × 0.001 (0.1%)"
    echo "   - 检查余额减少是否主要来自手续费"
    echo ""
    echo -e "${BLUE}3. 持仓管理验证:${NC}"
    echo "   - 开仓: 新建持仓，数量和价格正确"
    echo "   - 加仓: 持仓数量增加，平均价格重新计算"
    echo "   - 减仓: 持仓数量减少，平均价格不变"
    echo "   - 平仓: 持仓清零，计算已实现盈亏"
    echo ""
    echo -e "${BLUE}4. 盈亏计算验证:${NC}"
    echo "   - 未实现盈亏随价格变化实时更新"
    echo "   - 已实现盈亏在平仓时正确计算"
    echo ""
    echo -e "${BLUE}5. 保证金机制验证:${NC}"
    echo "   - 期货交易使用保证金，不直接扣除本金"
    echo "   - 可用余额根据保证金冻结情况变化"
    echo ""
    
    # 5. 提供测试后检查脚本
    echo -e "${YELLOW}📋 测试后状态检查${NC}"
    echo "-" 
    echo "下单测试完成后，运行以下命令检查结果:"
    echo ""
    echo -e "${GREEN}./manual_order_test.sh check${NC}"
    echo ""
    
    echo -e "${GREEN}✅ 测试准备完成，可以开始下单${NC}"
}

# 检查模式
check_mode() {
    echo -e "${GREEN}🔍 测试后状态检查${NC}"
    echo ""
    
    show_account_info "测试后状态"
    
    echo -e "${YELLOW}📊 变化分析${NC}"
    echo "-" 
    echo "请手动比较测试前后的账户状态，验证:"
    echo "1. 余额变化是否合理 (主要是手续费扣除)"
    echo "2. 持仓是否正确建立/修改"
    echo "3. 未实现盈亏是否正确计算"
    echo "4. 保证金冻结是否正常"
    echo ""
}

# 主程序
if [ "$1" = "check" ]; then
    check_mode
else
    main
fi
