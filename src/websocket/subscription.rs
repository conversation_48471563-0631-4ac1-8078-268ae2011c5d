use crate::types::{ClientFormat, SubscriptionType};
use dashmap::DashMap;
use std::collections::HashSet;
use std::sync::Arc;
use tokio::sync::mpsc;
use tracing::info;
use uuid::Uuid;

/// 客户端ID类型
pub type ClientId = Uuid;

/// 客户端订阅信息
#[derive(Debug, Clone)]
pub struct ClientSubscription {
    pub client_id: ClientId,
    pub subscriptions: HashSet<SubscriptionType>,
    pub sender: mpsc::Sender<String>,
    pub format: ClientFormat, // 客户端期望的数据格式
}

/// 订阅管理器
#[derive(Debug)]
pub struct SubscriptionManager {
    /// 客户端订阅映射
    clients: Arc<DashMap<ClientId, ClientSubscription>>,
}

impl SubscriptionManager {
    /// 创建新的订阅管理器
    pub fn new() -> Self {
        Self {
            clients: Arc::new(DashMap::new()),
        }
    }

    /// 添加新客户端
    pub fn add_client(&self, sender: mpsc::Sender<String>) -> ClientId {
        self.add_client_with_format(sender, ClientFormat::Binance) // 默认使用Binance格式
    }

    /// 添加新客户端并指定格式
    pub fn add_client_with_format(
        &self,
        sender: mpsc::Sender<String>,
        format: ClientFormat,
    ) -> ClientId {
        let client_id = Uuid::new_v4();
        let subscription = ClientSubscription {
            client_id,
            subscriptions: HashSet::new(),
            sender,
            format: format.clone(),
        };

        self.clients.insert(client_id, subscription);
        tracing::info!(
            "New client connected: {} with format {:?}",
            client_id,
            format
        );

        client_id
    }

    /// 移除客户端
    pub fn remove_client(&self, client_id: &ClientId) {
        if self.clients.remove(client_id).is_some() {
            tracing::info!("Client disconnected: {}", client_id);
        }
    }

    /// 添加订阅
    pub fn subscribe(&self, client_id: &ClientId, subscription_type: SubscriptionType) -> bool {
        if let Some(mut client) = self.clients.get_mut(client_id) {
            let inserted = client.subscriptions.insert(subscription_type.clone());
            if inserted {
                tracing::info!("Client {} subscribed to {:?}", client_id, subscription_type);
            }
            inserted
        } else {
            tracing::warn!(
                "Attempted to subscribe for non-existent client: {}",
                client_id
            );
            false
        }
    }

    /// 取消订阅
    pub fn unsubscribe(&self, client_id: &ClientId, subscription_type: &SubscriptionType) -> bool {
        if let Some(mut client) = self.clients.get_mut(client_id) {
            let removed = client.subscriptions.remove(subscription_type);
            if removed {
                tracing::info!(
                    "Client {} unsubscribed from {:?}",
                    client_id,
                    subscription_type
                );
            }
            removed
        } else {
            tracing::warn!(
                "Attempted to unsubscribe for non-existent client: {}",
                client_id
            );
            false
        }
    }

    /// 获取订阅了特定类型的所有客户端
    pub fn get_subscribers(&self, subscription_type: &SubscriptionType) -> Vec<ClientId> {
        self.clients
            .iter()
            .filter_map(|entry| {
                let client = entry.value();
                if client.subscriptions.contains(subscription_type) {
                    Some(client.client_id)
                } else {
                    None
                }
            })
            .collect()
    }

    /// 获取订阅了特定类型的所有客户端详细信息
    pub fn get_subscriber_details(
        &self,
        subscription_type: &SubscriptionType,
    ) -> Vec<ClientSubscription> {
        self.clients
            .iter()
            .filter_map(|entry| {
                let client = entry.value();
                if client.subscriptions.contains(subscription_type) {
                    Some(client.clone())
                } else {
                    None
                }
            })
            .collect()
    }

    /// 向特定客户端发送消息
    pub async fn send_to_client(&self, client_id: &ClientId, message: String) -> bool {
        if let Some(client) = self.clients.get(client_id) {
            match client.sender.send(message).await {
                Ok(()) => true,
                Err(e) => {
                    tracing::error!("Failed to send message to client {}: {}", client_id, e);
                    // 客户端可能已断开连接，移除它
                    drop(client);
                    self.remove_client(client_id);
                    false
                }
            }
        } else {
            tracing::warn!(
                "Attempted to send message to non-existent client: {}",
                client_id
            );
            false
        }
    }

    /// 向所有订阅了特定类型的客户端广播消息
    pub async fn broadcast(&self, subscription_type: &SubscriptionType, message: String) {
        let subscribers = self.get_subscribers(subscription_type);
        let mut failed_clients = Vec::new();

        for client_id in subscribers {
            if subscription_type == &SubscriptionType::OrderAndFill {
                tracing::info!("Sending order update to client {}", client_id);
            }
            if !self.send_to_client(&client_id, message.clone()).await {
                failed_clients.push(client_id);
            }
        }

        // 清理失败的客户端
        for client_id in failed_clients {
            self.remove_client(&client_id);
        }

        tracing::debug!(
            "Broadcasted message for {:?} subscription",
            subscription_type
        );
    }

    /// 获取客户端订阅信息
    pub fn get_client_subscriptions(
        &self,
        client_id: &ClientId,
    ) -> Option<HashSet<SubscriptionType>> {
        self.clients
            .get(client_id)
            .map(|client| client.subscriptions.clone())
    }

    /// 获取所有客户端数量
    pub fn client_count(&self) -> usize {
        self.clients.len()
    }

    /// 获取特定订阅类型的客户端数量
    pub fn subscription_count(&self, subscription_type: &SubscriptionType) -> usize {
        self.clients
            .iter()
            .filter(|entry| entry.value().subscriptions.contains(subscription_type))
            .count()
    }

    /// 获取所有客户端ID
    pub fn get_all_clients(&self) -> Vec<ClientId> {
        self.clients.iter().map(|entry| *entry.key()).collect()
    }

    /// 清理所有客户端
    pub fn clear(&self) {
        let client_count = self.clients.len();
        self.clients.clear();
        tracing::info!("Cleared {} clients", client_count);
    }
}

impl Default for SubscriptionManager {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tokio::sync::mpsc;

    #[tokio::test]
    async fn test_subscription_manager() {
        let manager = SubscriptionManager::new();
        let (tx, _rx) = mpsc::channel(100);

        // 添加客户端
        let client_id = manager.add_client(tx);
        assert_eq!(manager.client_count(), 1);

        // 添加订阅
        assert!(manager.subscribe(&client_id, SubscriptionType::OrderBook));
        assert!(manager.subscribe(&client_id, SubscriptionType::Trade));

        // 检查订阅
        let subscribers = manager.get_subscribers(&SubscriptionType::OrderBook);
        assert_eq!(subscribers.len(), 1);
        assert_eq!(subscribers[0], client_id);

        // 取消订阅
        assert!(manager.unsubscribe(&client_id, &SubscriptionType::OrderBook));
        let subscribers = manager.get_subscribers(&SubscriptionType::OrderBook);
        assert_eq!(subscribers.len(), 0);

        // 移除客户端
        manager.remove_client(&client_id);
        assert_eq!(manager.client_count(), 0);
    }
}
