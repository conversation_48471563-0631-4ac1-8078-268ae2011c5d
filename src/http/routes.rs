use crate::http::handlers;
use std::net::SocketAddr;
use warp::{Filter, Reply};

/// 创建所有API路由
pub fn create_routes() -> impl Filter<Extract = impl Reply> + Clone {
    let cors = warp::cors()
        .allow_any_origin()
        .allow_headers(vec!["content-type"])
        .allow_methods(vec!["GET", "POST", "PUT", "DELETE"]);

    // 添加请求日志中间件
    let log = warp::log::custom(|info| {
        tracing::info!(
            "🌐 HTTP Request: {} {} - Status: {} - Duration: {:?} - Remote: {:?}",
            info.method(),
            info.path(),
            info.status(),
            info.elapsed(),
            info.remote_addr()
        );
    });

    // 添加连接级别的日志中间件
    let connection_log = warp::any()
        .and(warp::addr::remote())
        .and_then(|remote_addr: Option<SocketAddr>| async move {
            tracing::info!("🔗 New connection from: {:?}", remote_addr);
            Ok::<_, warp::Rejection>(())
        })
        .untuple_one();

    let api = warp::path("api").and(warp::path("v1"));
    let fapi = warp::path("fapi").and(warp::path("v1"));
    let fapi_v2 = warp::path("fapi").and(warp::path("v2"));
    let okx_api = warp::path("api").and(warp::path("v5"));

    // fapi/v1/listenKey
    let listen_key_post = fapi
        .and(warp::path("listenKey"))
        .and(warp::path::end())
        .and(warp::post())
        .and_then(handlers::listen_key_handler);

    let listen_key_put = fapi
        .and(warp::path("listenKey"))
        .and(warp::path::end())
        .and(warp::put())
        .and_then(handlers::listen_key_handler);

    let okx_root = okx_api
        .and(warp::path::end())
        .and(warp::get())
        .and_then(handlers::okx_root_handler);

    // 健康检查路由
    let health = api
        .and(warp::path("health"))
        .and(warp::path::end())
        .and(warp::get())
        .and_then(handlers::health_handler);

    // 配置信息路由
    let config = api
        .and(warp::path("config"))
        .and(warp::path::end())
        .and(warp::get())
        .and_then(handlers::config_handler);

    // 订单簿快照路由
    let orderbook = api
        .and(warp::path("orderbook"))
        .and(warp::path::end())
        .and(warp::get())
        .and_then(handlers::orderbook_handler);

    // 交易记录路由
    let trades = api
        .and(warp::path("trades"))
        .and(warp::path::end())
        .and(warp::get())
        .and_then(handlers::trades_handler);

    // 统计信息路由
    let stats = api
        .and(warp::path("stats"))
        .and(warp::path::end())
        .and(warp::get())
        .and_then(handlers::stats_handler);

    // 技术指标路由
    let indicators = api
        .and(warp::path("indicators"))
        .and(warp::path::end())
        .and(warp::get())
        .and_then(handlers::indicators_handler);

    // Binance API 兼容路由 - exchangeInfo
    let exchange_info = fapi
        .and(warp::path("exchangeInfo"))
        .and(warp::path::end())
        .and(warp::get())
        .and_then(handlers::exchange_info_handler);

    // Binance Futures API 兼容路由 - account
    let futures_account = fapi
        .and(warp::path("account"))
        .and(warp::path::end())
        .and(warp::get())
        .and_then(handlers::futures_account_handler);

    // 数据流控制路由
    let datastream_status = api
        .and(warp::path("datastream"))
        .and(warp::path("status"))
        .and(warp::path::end())
        .and(warp::get())
        .and_then(handlers::datastream_status_handler);

    let datastream_start = api
        .and(warp::path("datastream"))
        .and(warp::path("start"))
        .and(warp::path::end())
        .and(warp::post())
        .and_then(handlers::datastream_start_handler);

    let datastream_stop = api
        .and(warp::path("datastream"))
        .and(warp::path("stop"))
        .and(warp::path::end())
        .and(warp::post())
        .and_then(handlers::datastream_stop_handler);

    let datastream_pause = api
        .and(warp::path("datastream"))
        .and(warp::path("pause"))
        .and(warp::path::end())
        .and(warp::post())
        .and_then(handlers::datastream_pause_handler);

    let datastream_resume = api
        .and(warp::path("datastream"))
        .and(warp::path("resume"))
        .and(warp::path::end())
        .and(warp::post())
        .and_then(handlers::datastream_resume_handler);

    let datastream_config = api
        .and(warp::path("datastream"))
        .and(warp::path("config"))
        .and(warp::path::end())
        .and(warp::put())
        .and(warp::body::json())
        .and_then(handlers::datastream_config_handler);

    let futures_balance = fapi_v2
        .and(warp::path("balance"))
        .and(warp::path::end())
        .and(warp::get())
        .and_then(handlers::futures_balance_handler);

    // 根路由
    let root = warp::path::end()
        .and(warp::get())
        .map(|| {
            warp::reply::html(
                r#"
                <!DOCTYPE html>
                <html>
                <head>
                    <title>Backtest API Server</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 40px; }
                        .container { max-width: 800px; margin: 0 auto; }
                        .endpoint { background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 5px; }
                        .method { color: #007acc; font-weight: bold; }
                    </style>
                </head>
                <body>
                    <div class="container">
                        <h1>Backtest API Server</h1>
                        <p>Welcome to the Backtest API Server. Below are the available endpoints:</p>

                        <h2>Available Endpoints</h2>

                        <div class="endpoint">
                            <span class="method">GET</span> /api/v1/health
                            <p>Health check endpoint</p>
                        </div>

                        <div class="endpoint">
                            <span class="method">GET</span> /api/v1/config
                            <p>Get current configuration</p>
                        </div>

                        <div class="endpoint">
                            <span class="method">GET</span> /api/v1/orderbook
                            <p>Get current orderbook snapshot</p>
                        </div>

                        <div class="endpoint">
                            <span class="method">GET</span> /api/v1/trades
                            <p>Get recent trades</p>
                        </div>

                        <div class="endpoint">
                            <span class="method">GET</span> /api/v1/stats
                            <p>Get system statistics</p>
                        </div>

                        <div class="endpoint">
                            <span class="method">GET</span> /api/v1/indicators
                            <p>Get technical indicators</p>
                        </div>

                        <div class="endpoint">
                            <span class="method">GET</span> /fapi/v1/exchangeInfo
                            <p>Binance API compatible exchange information</p>
                        </div>

                        <h2>Data Stream Control</h2>

                        <div class="endpoint">
                            <span class="method">GET</span> /api/v1/datastream/status
                            <p>Get data stream status and configuration</p>
                        </div>

                        <div class="endpoint">
                            <span class="method">POST</span> /api/v1/datastream/start
                            <p>Start data stream</p>
                        </div>

                        <div class="endpoint">
                            <span class="method">POST</span> /api/v1/datastream/stop
                            <p>Stop data stream</p>
                        </div>

                        <div class="endpoint">
                            <span class="method">POST</span> /api/v1/datastream/pause
                            <p>Pause data stream</p>
                        </div>

                        <div class="endpoint">
                            <span class="method">POST</span> /api/v1/datastream/resume
                            <p>Resume data stream</p>
                        </div>

                        <div class="endpoint">
                            <span class="method">PUT</span> /api/v1/datastream/config
                            <p>Update data stream configuration</p>
                        </div>

                        <h2>WebSocket</h2>
                        <p>WebSocket server is available for real-time data streaming.</p>

                        <h2>Documentation</h2>
                        <p>For more information, please refer to the API documentation.</p>
                    </div>
                </body>
                </html>
                "#
            )
        });

    // 组合所有路由
    let routes = connection_log
        .and(
            root.or(health)
                .or(config)
                .or(orderbook)
                .or(trades)
                .or(stats)
                .or(indicators)
                .or(exchange_info)
                .or(futures_account)
                .or(datastream_status)
                .or(datastream_start)
                .or(datastream_stop)
                .or(datastream_pause)
                .or(datastream_resume)
                .or(listen_key_post)
                .or(listen_key_put)
                .or(datastream_config)
                .or(futures_balance)
                .or(okx_root),
        )
        .with(cors)
        .with(log)
        .recover(handlers::handle_rejection);

    routes
}

#[cfg(test)]
mod tests {
    use super::*;
    use warp::test;

    #[tokio::test]
    async fn test_health_route() {
        let routes = create_routes();

        let resp = test::request()
            .method("GET")
            .path("/api/v1/health")
            .reply(&routes)
            .await;

        assert_eq!(resp.status(), 200);
    }

    #[tokio::test]
    async fn test_exchange_info_route() {
        let routes = create_routes();

        let resp = test::request()
            .method("GET")
            .path("/fapi/v1/exchangeInfo")
            .reply(&routes)
            .await;

        assert_eq!(resp.status(), 200);
    }

    #[tokio::test]
    async fn test_config_route() {
        let routes = create_routes();

        let resp = test::request()
            .method("GET")
            .path("/api/v1/config")
            .reply(&routes)
            .await;

        assert_eq!(resp.status(), 200);
    }

    #[tokio::test]
    async fn test_root_route() {
        let routes = create_routes();

        let resp = test::request().method("GET").path("/").reply(&routes).await;

        assert_eq!(resp.status(), 200);
        assert!(resp.body().len() > 0);
    }
}
