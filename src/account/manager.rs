use crate::account::account::{Account, AccountSummary};
use crate::account::types::{AccountConfig, TradeRecord};
use crate::types::{Price, Trade};
use crate::{BacktestError, Result};
use std::collections::HashMap;

/// 账户管理器
pub struct AccountManager {
    /// 账户实例
    account: Account,
    /// 当前市场价格
    current_prices: HashMap<String, Price>,
}

impl AccountManager {
    /// 创建新的账户管理器
    pub fn new(account_id: String, config: AccountConfig) -> Self {
        let account = Account::new(account_id, config);

        Self {
            account,
            current_prices: HashMap::new(),
        }
    }

    /// 处理交易
    pub fn process_trade(&mut self, trade: Trade) -> Result<()> {
        // 获取账户配置中的手续费率
        let fee_rate = self.account.config.taker_fee_rate;

        let trade_record = TradeRecord::new(
            trade.id.clone(),
            format!("order_{}", trade.id), // 假设订单ID
            trade.symbol.clone(),          // 从trade中获取交易对
            trade.side,
            trade.price,
            trade.quantity,
            trade.quantity * trade.price.value() * fee_rate, // 使用配置的手续费率
            "USDT".to_string(),
            false, // 假设为taker
        );

        self.account
            .process_trade(trade_record, &self.current_prices)
            .map_err(|e| BacktestError::Account(e))?;

        Ok(())
    }

    /// 更新市场价格
    pub fn update_price(&mut self, symbol: String, price: Price) {
        self.current_prices.insert(symbol, price);
    }

    /// 获取账户摘要
    pub fn get_account_summary(&self) -> AccountSummary {
        self.account.get_summary(&self.current_prices)
    }

    /// 获取账户可用余额
    pub fn get_balance(&self, asset: &str) -> f64 {
        self.account.balance_manager.get_available_balance(asset)
    }

    /// 获取账户总余额
    pub fn get_total_balance(&self, asset: &str) -> f64 {
        self.account.balance_manager.get_total_balance(asset)
    }

    /// 获取仓位信息
    pub fn get_position(&self, symbol: &str) -> Option<&crate::account::position::Position> {
        self.account.get_position(symbol)
    }

    /// 获取账户净值
    pub fn get_net_value(&self) -> f64 {
        self.account.calculate_net_value(&self.current_prices)
    }

    /// 验证账户状态
    pub fn validate_account(&self) -> Result<()> {
        self.account
            .validate()
            .map_err(|e| BacktestError::Account(e))
    }

    /// 获取taker手续费率
    pub fn get_taker_fee_rate(&self) -> f64 {
        self.account.config.taker_fee_rate
    }

    /// 获取maker手续费率
    pub fn get_maker_fee_rate(&self) -> f64 {
        self.account.config.maker_fee_rate
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::types::OrderSide;

    #[test]
    fn test_account_manager_creation() {
        let config = AccountConfig::default();
        let manager = AccountManager::new("test_account".to_string(), config.clone());

        let summary = manager.get_account_summary();
        assert_eq!(summary.account_id, "test_account");
        assert_eq!(summary.stats.available_balance, config.initial_balance);
    }

    #[test]
    fn test_account_manager_process_trade() {
        let config = AccountConfig::default();
        let mut manager = AccountManager::new("test_account".to_string(), config);

        // 更新价格
        manager.update_price("BTCUSDT".to_string(), Price::new(50000.0));

        // 创建交易
        let trade = Trade {
            id: "trade1".to_string(),
            symbol: "BTCUSDT".to_string(),
            price: Price::new(50000.0),
            quantity: 0.1,
            side: OrderSide::Buy,
            timestamp: Some(chrono::Utc::now()),
        };

        let result = manager.process_trade(trade);
        assert!(result.is_ok());

        // 检查余额变化
        let balance = manager.get_balance("USDT");
        assert!(balance < 10000.0); // 余额应该减少

        // 检查仓位
        let position = manager.get_position("BTCUSDT");
        assert!(position.is_some());
        let pos = position.unwrap();
        assert_eq!(pos.quantity, 0.1);
    }

    #[test]
    fn test_account_manager_price_update() {
        let config = AccountConfig::default();
        let mut manager = AccountManager::new("test_account".to_string(), config);

        // 更新价格
        manager.update_price("BTCUSDT".to_string(), Price::new(50000.0));
        manager.update_price("ETHUSDT".to_string(), Price::new(3000.0));

        // 价格应该被正确存储
        assert_eq!(
            manager.current_prices.get("BTCUSDT").unwrap().value(),
            50000.0
        );
        assert_eq!(
            manager.current_prices.get("ETHUSDT").unwrap().value(),
            3000.0
        );
    }

    #[test]
    fn test_account_manager_net_value() {
        let config = AccountConfig::default();
        let mut manager = AccountManager::new("test_account".to_string(), config);

        // 更新价格
        manager.update_price("BTCUSDT".to_string(), Price::new(50000.0));

        // 处理买入交易
        let trade = Trade {
            id: "trade1".to_string(),
            symbol: "BTCUSDT".to_string(),
            price: Price::new(50000.0),
            quantity: 0.1,
            side: OrderSide::Buy,
            timestamp: Some(chrono::Utc::now()),
        };
        manager.process_trade(trade).unwrap();

        // 价格上涨
        manager.update_price("BTCUSDT".to_string(), Price::new(55000.0));

        let net_value = manager.get_net_value();

        // 计算预期净值：初始余额 - 交易成本 + 未实现盈亏
        let trade_cost = 50000.0 * 0.1 + 0.1 * 50000.0 * 0.0004; // 成本 + 手续费
        let unrealized_pnl = 0.1 * (55000.0 - 50000.0); // 未实现盈亏
        let expected_net_value = 10000.0 - trade_cost + unrealized_pnl;

        println!(
            "Net value: {}, Expected: {}, Trade cost: {}, Unrealized PnL: {}",
            net_value, expected_net_value, trade_cost, unrealized_pnl
        );

        // 由于价格上涨，净值应该比初始成本高
        assert!(net_value > (10000.0 - trade_cost)); // 净值应该比扣除交易成本后的余额高
    }

    #[test]
    fn test_account_manager_validation() {
        let config = AccountConfig::default();
        let manager = AccountManager::new("test_account".to_string(), config);

        let result = manager.validate_account();
        assert!(result.is_ok());
    }

    #[test]
    fn test_account_manager_multiple_trades() {
        let config = AccountConfig::default();
        let mut manager = AccountManager::new("test_account".to_string(), config);

        // 更新价格
        manager.update_price("BTCUSDT".to_string(), Price::new(50000.0));

        // 第一笔交易：买入
        let trade1 = Trade {
            id: "trade1".to_string(),
            symbol: "BTCUSDT".to_string(),
            price: Price::new(50000.0),
            quantity: 0.1,
            side: OrderSide::Buy,
            timestamp: Some(chrono::Utc::now()),
        };
        manager.process_trade(trade1).unwrap();

        // 第二笔交易：再次买入（加仓）
        let trade2 = Trade {
            id: "trade2".to_string(),
            symbol: "BTCUSDT".to_string(),
            price: Price::new(52000.0),
            quantity: 0.05,
            side: OrderSide::Buy,
            timestamp: Some(chrono::Utc::now()),
        };
        manager.process_trade(trade2).unwrap();

        // 检查仓位
        let position = manager.get_position("BTCUSDT").unwrap();
        assert!((position.quantity - 0.15).abs() < 1e-10); // 总持仓应该是0.15

        // 检查平均价格
        let expected_avg_price = (50000.0 * 0.1 + 52000.0 * 0.05) / 0.15;
        assert!((position.avg_price.value() - expected_avg_price).abs() < 1e-6);
    }
}
