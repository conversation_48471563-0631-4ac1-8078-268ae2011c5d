use crate::types::{OrderSide, Price};
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};

/// 账户类型
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum AccountType {
    /// 现货账户
    Spot,
    /// 合约账户（支持杠杆）
    Futures,
    /// 期权账户
    Options,
}

/// 仓位方向
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum PositionSide {
    /// 多头仓位
    Long,
    /// 空头仓位
    Short,
    /// 无仓位
    None,
}

impl From<OrderSide> for PositionSide {
    fn from(order_side: OrderSide) -> Self {
        match order_side {
            OrderSide::Buy => PositionSide::Long,
            OrderSide::Sell => PositionSide::Short,
        }
    }
}

/// 保证金模式
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum MarginMode {
    /// 全仓模式
    Cross,
    /// 逐仓模式
    Isolated,
}

/// 账户配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AccountConfig {
    /// 账户类型
    pub account_type: AccountType,
    /// 初始资金（USDT）
    pub initial_balance: f64,
    /// 最大杠杆倍数
    pub max_leverage: f64,
    /// 保证金模式
    pub margin_mode: MarginMode,
    /// 手续费率（maker）
    pub maker_fee_rate: f64,
    /// 手续费率（taker）
    pub taker_fee_rate: f64,
    /// 资金费率（合约）
    pub funding_rate: f64,
    /// 支持的交易对
    pub supported_symbols: Vec<String>,
}

impl Default for AccountConfig {
    fn default() -> Self {
        Self {
            account_type: AccountType::Futures,
            initial_balance: 10000.0, // 默认1万USDT
            max_leverage: 10.0,       // 默认10倍杠杆
            margin_mode: MarginMode::Cross,
            maker_fee_rate: 0.0002, // 0.02%
            taker_fee_rate: 0.0004, // 0.04%
            funding_rate: 0.0001,   // 0.01%
            supported_symbols: vec!["BTCUSDT".to_string()],
        }
    }
}

/// 交易记录
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TradeRecord {
    /// 交易ID
    pub trade_id: String,
    /// 订单ID
    pub order_id: String,
    /// 交易对
    pub symbol: String,
    /// 交易方向
    pub side: OrderSide,
    /// 交易价格
    pub price: Price,
    /// 交易数量
    pub quantity: f64,
    /// 手续费
    pub fee: f64,
    /// 手续费币种
    pub fee_asset: String,
    /// 交易时间
    pub timestamp: DateTime<Utc>,
    /// 是否为maker
    pub is_maker: bool,
}

impl TradeRecord {
    /// 创建新的交易记录
    pub fn new(
        trade_id: String,
        order_id: String,
        symbol: String,
        side: OrderSide,
        price: Price,
        quantity: f64,
        fee: f64,
        fee_asset: String,
        is_maker: bool,
    ) -> Self {
        Self {
            trade_id,
            order_id,
            symbol,
            side,
            price,
            quantity,
            fee,
            fee_asset,
            timestamp: Utc::now(),
            is_maker,
        }
    }

    /// 计算交易金额（不含手续费）
    pub fn notional_value(&self) -> f64 {
        self.price.value() * self.quantity
    }

    /// 计算净交易金额（含手续费）
    pub fn net_value(&self) -> f64 {
        match self.side {
            OrderSide::Buy => self.notional_value() + self.fee,
            OrderSide::Sell => self.notional_value() - self.fee,
        }
    }
}

/// 账户统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AccountStats {
    /// 总资产价值（USDT）
    pub total_value: f64,
    /// 可用余额（USDT）
    pub available_balance: f64,
    /// 已用保证金（USDT）
    pub used_margin: f64,
    /// 未实现盈亏（USDT）
    pub unrealized_pnl: f64,
    /// 已实现盈亏（USDT）
    pub realized_pnl: f64,
    /// 当前杠杆倍数
    pub current_leverage: f64,
    /// 保证金率
    pub margin_ratio: f64,
    /// 持仓数量
    pub position_count: usize,
    /// 总交易次数
    pub total_trades: usize,
    /// 总手续费（USDT）
    pub total_fees: f64,
    /// 最后更新时间
    pub last_updated: DateTime<Utc>,
}

impl Default for AccountStats {
    fn default() -> Self {
        Self {
            total_value: 0.0,
            available_balance: 0.0,
            used_margin: 0.0,
            unrealized_pnl: 0.0,
            realized_pnl: 0.0,
            current_leverage: 0.0,
            margin_ratio: 0.0,
            position_count: 0,
            total_trades: 0,
            total_fees: 0.0,
            last_updated: Utc::now(),
        }
    }
}

/// 风险指标
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskMetrics {
    /// 最大回撤
    pub max_drawdown: f64,
    /// 夏普比率
    pub sharpe_ratio: f64,
    /// 胜率
    pub win_rate: f64,
    /// 盈亏比
    pub profit_loss_ratio: f64,
    /// VaR (Value at Risk)
    pub var_95: f64,
    /// 最大单笔亏损
    pub max_loss: f64,
    /// 最大单笔盈利
    pub max_profit: f64,
}

impl Default for RiskMetrics {
    fn default() -> Self {
        Self {
            max_drawdown: 0.0,
            sharpe_ratio: 0.0,
            win_rate: 0.0,
            profit_loss_ratio: 0.0,
            var_95: 0.0,
            max_loss: 0.0,
            max_profit: 0.0,
        }
    }
}
