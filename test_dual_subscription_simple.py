#!/usr/bin/env python3
"""
简单的WebSocket客户端测试脚本
测试同时订阅Binance和OKX的BBO和trades数据
"""

import asyncio
import json
import ssl
import time
from datetime import datetime

try:
    import websockets
except ImportError:
    print("请安装websockets: pip install websockets")
    exit(1)

class SimpleWebSocketClient:
    def __init__(self, uri, name, format_type):
        self.uri = uri
        self.name = name
        self.format_type = format_type
        self.websocket = None
        self.message_count = 0

    async def connect(self):
        """连接到WebSocket服务器"""
        try:
            # 创建SSL上下文，忽略证书验证（仅用于测试）
            ssl_context = ssl.create_default_context()
            ssl_context.check_hostname = False
            ssl_context.verify_mode = ssl.CERT_NONE

            print(f"[{self.name}] 连接到 {self.uri}...")
            self.websocket = await websockets.connect(self.uri, ssl=ssl_context)
            print(f"[{self.name}] ✅ 连接成功")
            return True
        except Exception as e:
            print(f"[{self.name}] ❌ 连接失败: {e}")
            return False

    async def subscribe_binance(self):
        """订阅Binance格式的数据"""
        # 订阅BBO数据 (bookTicker)
        bbo_msg = {
            "method": "SUBSCRIBE",
            "params": ["btcusdt@bookTicker"],
            "id": 1
        }
        await self.websocket.send(json.dumps(bbo_msg))
        print(f"[{self.name}] 📡 发送Binance BBO订阅: {bbo_msg}")

        # 订阅trades数据
        trades_msg = {
            "method": "SUBSCRIBE",
            "params": ["btcusdt@trade"],
            "id": 2
        }
        await self.websocket.send(json.dumps(trades_msg))
        print(f"[{self.name}] 📡 发送Binance trades订阅: {trades_msg}")

    async def subscribe_okx(self):
        """订阅OKX格式的数据"""
        # 订阅BBO数据 (tickers)
        bbo_msg = {
            "op": "subscribe",
            "args": [
                {
                    "channel": "tickers",
                    "instId": "BTC-USDT"
                }
            ]
        }
        await self.websocket.send(json.dumps(bbo_msg))
        print(f"[{self.name}] 📡 发送OKX BBO订阅: {bbo_msg}")

        # 订阅trades数据
        trades_msg = {
            "op": "subscribe",
            "args": [
                {
                    "channel": "trades",
                    "instId": "BTC-USDT"
                }
            ]
        }
        await self.websocket.send(json.dumps(trades_msg))
        print(f"[{self.name}] 📡 发送OKX trades订阅: {trades_msg}")

    async def listen(self, duration=30):
        """监听消息"""
        print(f"[{self.name}] 🎧 开始监听消息，持续 {duration} 秒...")
        start_time = time.time()
        bbo_count = 0
        trades_count = 0

        try:
            while time.time() - start_time < duration:
                try:
                    message = await asyncio.wait_for(self.websocket.recv(), timeout=1.0)
                    data = json.loads(message)
                    self.message_count += 1

                    timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]

                    if self.format_type == "binance":
                        if "stream" in data:
                            stream = data["stream"]
                            if "bookTicker" in stream:
                                bbo_count += 1
                                print(f"[{timestamp}] {self.name} BBO #{bbo_count}: bid={data['data'].get('b', 'N/A')}, ask={data['data'].get('a', 'N/A')}")
                            elif "trade" in stream:
                                trades_count += 1
                                print(f"[{timestamp}] {self.name} Trade #{trades_count}: price={data['data'].get('p', 'N/A')}, qty={data['data'].get('q', 'N/A')}")
                        else:
                            print(f"[{timestamp}] {self.name} Response: {data}")
                    else:  # okx
                        if "arg" in data and "data" in data:
                            channel = data["arg"]["channel"]
                            if channel == "tickers":
                                bbo_count += 1
                                ticker_data = data["data"][0] if data["data"] else {}
                                print(f"[{timestamp}] {self.name} BBO #{bbo_count}: bid={ticker_data.get('bidPx', 'N/A')}, ask={ticker_data.get('askPx', 'N/A')}")
                            elif channel == "trades":
                                trades_count += 1
                                trade_data = data["data"][0] if data["data"] else {}
                                print(f"[{timestamp}] {self.name} Trade #{trades_count}: price={trade_data.get('px', 'N/A')}, qty={trade_data.get('sz', 'N/A')}")
                        else:
                            print(f"[{timestamp}] {self.name} Response: {data}")

                except asyncio.TimeoutError:
                    continue
                except Exception as e:
                    print(f"[{self.name}] ⚠️ 接收消息错误: {e}")
                    break

        except Exception as e:
            print(f"[{self.name}] ❌ 监听循环错误: {e}")

        print(f"[{self.name}] 📊 统计: 总消息={self.message_count}, BBO={bbo_count}, Trades={trades_count}")
        return {"total": self.message_count, "bbo": bbo_count, "trades": trades_count}

    async def close(self):
        """关闭连接"""
        if self.websocket:
            await self.websocket.close()
            print(f"[{self.name}] 🔌 连接已关闭")

async def test_dual_subscription():
    """测试双重订阅"""
    print("=" * 60)
    print("🚀 测试OKX和Binance同时订阅BBO和Trades数据")
    print("=" * 60)
    print()

    # 创建两个客户端
    binance_client = SimpleWebSocketClient("wss://localhost:8082", "Binance", "binance")
    okx_client = SimpleWebSocketClient("wss://localhost:8082", "OKX", "okx")

    try:
        # 连接到两个服务器
        print("🔗 正在连接到服务器...")
        binance_connected = await binance_client.connect()
        okx_connected = await okx_client.connect()

        if not binance_connected:
            print("❌ Binance服务器连接失败，请确保服务器在端口8082运行")
            print("   启动命令: cargo run --bin backtest -- --config example_config.toml")
            return

        if not okx_connected:
            print("❌ OKX服务器连接失败，请确保服务器在端口8084运行")
            print("   启动命令: cargo run --bin backtest -- --config okx_config.toml")
            return

        print()
        print("📡 发送订阅请求...")

        # 发送订阅请求
        await binance_client.subscribe_binance()
        await okx_client.subscribe_okx()

        # 等待订阅生效
        print()
        print("⏳ 等待订阅生效...")
        await asyncio.sleep(3)

        # 同时监听两个连接
        print()
        print("🎧 开始同时监听两个连接...")
        print("-" * 60)

        results = await asyncio.gather(
            binance_client.listen(30),
            okx_client.listen(30),
            return_exceptions=True
        )

        print("-" * 60)
        print()
        print("📊 测试结果:")
        print("=" * 60)

        binance_result = results[0] if not isinstance(results[0], Exception) else {"total": 0, "bbo": 0, "trades": 0}
        okx_result = results[1] if not isinstance(results[1], Exception) else {"total": 0, "bbo": 0, "trades": 0}

        print(f"Binance 服务器:")
        print(f"  📈 BBO消息: {binance_result['bbo']}")
        print(f"  💰 Trades消息: {binance_result['trades']}")
        print(f"  📊 总消息: {binance_result['total']}")
        print()

        print(f"OKX 服务器:")
        print(f"  📈 BBO消息: {okx_result['bbo']}")
        print(f"  💰 Trades消息: {okx_result['trades']}")
        print(f"  📊 总消息: {okx_result['total']}")
        print()

        # 评估测试结果
        total_messages = binance_result['total'] + okx_result['total']
        total_bbo = binance_result['bbo'] + okx_result['bbo']
        total_trades = binance_result['trades'] + okx_result['trades']

        print("🎯 测试评估:")
        if total_messages > 0:
            print("✅ 成功: 接收到数据")
            if binance_result['total'] > 0 and okx_result['total'] > 0:
                print("✅ 成功: 两个服务器都有数据")
                if total_bbo > 0 and total_trades > 0:
                    print("✅ 成功: 同时接收到BBO和Trades数据")
                    print("🎉 完美! 双重订阅功能正常工作!")
                else:
                    print("⚠️  部分成功: 只接收到一种数据类型")
            else:
                print("⚠️  部分成功: 只有一个服务器有数据")
        else:
            print("❌ 失败: 未接收到任何数据")
            print("   请检查服务器是否正在运行并且数据流已启动")

        print()
        print(f"📈 总计: {total_messages} 消息 ({total_bbo} BBO + {total_trades} Trades)")

    except Exception as e:
        print(f"❌ 测试失败: {e}")
    finally:
        # 关闭连接
        print()
        print("🔌 关闭连接...")
        await binance_client.close()
        await okx_client.close()

async def main():
    """主函数"""
    await test_dual_subscription()

if __name__ == "__main__":
    asyncio.run(main())
