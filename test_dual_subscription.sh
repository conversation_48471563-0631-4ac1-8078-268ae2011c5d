#!/bin/bash

echo "=== Testing OKX and Binance Simultaneous Subscription ==="
echo

# 检查服务器是否运行
echo "Checking if servers are running..."

# 检查Binance服务器 (端口8082)
if ! nc -z localhost 8082; then
    echo "❌ Binance server (port 8082) is not running"
    echo "Please start it with: cargo run --bin backtest -- --config example_config.toml"
    exit 1
fi
echo "✅ Binance server (port 8082) is running"

# 检查OKX服务器 (端口8084)
if ! nc -z localhost 8084; then
    echo "❌ OKX server (port 8084) is not running"
    echo "Please start it with: cargo run --bin backtest -- --config okx_config.toml"
    exit 1
fi
echo "✅ OKX server (port 8084) is running"

echo
echo "=== Starting WebSocket clients ==="

# 创建日志目录
mkdir -p test_logs

# 启动Binance客户端（后台运行）
echo "Starting Binance client..."
timeout 30s cargo run --bin websocket_client -- --host localhost --port 8082 --mode binance --duration 25 > test_logs/binance_client.log 2>&1 &
BINANCE_PID=$!

# 等待一下
sleep 2

# 启动OKX客户端（后台运行）
echo "Starting OKX client..."
timeout 30s cargo run --bin websocket_client -- --host localhost --port 8084 --mode okx --duration 25 > test_logs/okx_client.log 2>&1 &
OKX_PID=$!

echo "Both clients started, waiting for test completion..."
echo "Binance client PID: $BINANCE_PID"
echo "OKX client PID: $OKX_PID"

# 等待两个进程完成
wait $BINANCE_PID
BINANCE_EXIT=$?

wait $OKX_PID
OKX_EXIT=$?

echo
echo "=== Test Results ==="

# 检查Binance结果
echo "Binance client exit code: $BINANCE_EXIT"
if [ -f test_logs/binance_client.log ]; then
    BINANCE_MESSAGES=$(grep -c "Received message" test_logs/binance_client.log || echo "0")
    echo "Binance messages received: $BINANCE_MESSAGES"
    
    if [ $BINANCE_MESSAGES -gt 0 ]; then
        echo "✅ Binance client received data successfully"
        echo "Sample Binance messages:"
        grep "Received message" test_logs/binance_client.log | head -3
    else
        echo "❌ Binance client did not receive any messages"
        echo "Binance client log:"
        cat test_logs/binance_client.log
    fi
else
    echo "❌ Binance client log not found"
fi

echo

# 检查OKX结果
echo "OKX client exit code: $OKX_EXIT"
if [ -f test_logs/okx_client.log ]; then
    OKX_MESSAGES=$(grep -c "Received message" test_logs/okx_client.log || echo "0")
    echo "OKX messages received: $OKX_MESSAGES"
    
    if [ $OKX_MESSAGES -gt 0 ]; then
        echo "✅ OKX client received data successfully"
        echo "Sample OKX messages:"
        grep "Received message" test_logs/okx_client.log | head -3
    else
        echo "❌ OKX client did not receive any messages"
        echo "OKX client log:"
        cat test_logs/okx_client.log
    fi
else
    echo "❌ OKX client log not found"
fi

echo
echo "=== Summary ==="
if [ $BINANCE_MESSAGES -gt 0 ] && [ $OKX_MESSAGES -gt 0 ]; then
    echo "🎉 SUCCESS: Both Binance and OKX clients received data simultaneously!"
    echo "   - Binance: $BINANCE_MESSAGES messages"
    echo "   - OKX: $OKX_MESSAGES messages"
    echo "   - Test demonstrates successful dual subscription support"
else
    echo "❌ FAILURE: One or both clients failed to receive data"
    echo "   - Binance: $BINANCE_MESSAGES messages"
    echo "   - OKX: $OKX_MESSAGES messages"
fi

echo
echo "Full logs are available in:"
echo "  - test_logs/binance_client.log"
echo "  - test_logs/okx_client.log"
