#!/usr/bin/env python3
"""
测试OKX和Binance同时订阅的脚本
"""

import asyncio
import websockets
import json
import ssl
import sys
import time
from datetime import datetime

class WebSocketClient:
    def __init__(self, uri, client_format="binance"):
        self.uri = uri
        self.client_format = client_format
        self.websocket = None
        self.received_messages = []
        
    async def connect(self):
        """连接到WebSocket服务器"""
        try:
            # 创建SSL上下文，忽略证书验证（仅用于测试）
            ssl_context = ssl.create_default_context()
            ssl_context.check_hostname = False
            ssl_context.verify_mode = ssl.CERT_NONE
            
            print(f"Connecting to {self.uri} with {self.client_format} format...")
            self.websocket = await websockets.connect(self.uri, ssl=ssl_context)
            print(f"Connected to {self.uri}")
            return True
        except Exception as e:
            print(f"Failed to connect to {self.uri}: {e}")
            return False
    
    async def subscribe_binance_format(self):
        """使用Binance格式订阅"""
        # 订阅BookTicker数据
        subscribe_msg = {
            "method": "SUBSCRIBE",
            "params": ["btcusdt@bookTicker"],
            "id": 1
        }
        await self.websocket.send(json.dumps(subscribe_msg))
        print(f"Sent Binance subscription: {subscribe_msg}")
        
        # 订阅Trade数据
        trade_msg = {
            "method": "SUBSCRIBE", 
            "params": ["btcusdt@trade"],
            "id": 2
        }
        await self.websocket.send(json.dumps(trade_msg))
        print(f"Sent Binance trade subscription: {trade_msg}")
    
    async def subscribe_okx_format(self):
        """使用OKX格式订阅"""
        # 订阅Tickers数据（相当于BookTicker）
        subscribe_msg = {
            "op": "subscribe",
            "args": [
                {
                    "channel": "tickers",
                    "instId": "BTC-USDT"
                }
            ]
        }
        await self.websocket.send(json.dumps(subscribe_msg))
        print(f"Sent OKX tickers subscription: {subscribe_msg}")
        
        # 订阅Trades数据
        trade_msg = {
            "op": "subscribe",
            "args": [
                {
                    "channel": "trades",
                    "instId": "BTC-USDT"
                }
            ]
        }
        await self.websocket.send(json.dumps(trade_msg))
        print(f"Sent OKX trades subscription: {trade_msg}")
    
    async def listen(self, duration=30):
        """监听消息"""
        print(f"Listening for messages for {duration} seconds...")
        start_time = time.time()
        
        try:
            while time.time() - start_time < duration:
                try:
                    message = await asyncio.wait_for(self.websocket.recv(), timeout=1.0)
                    data = json.loads(message)
                    self.received_messages.append(data)
                    
                    # 打印收到的消息（简化显示）
                    timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
                    if self.client_format == "binance":
                        if "stream" in data:
                            print(f"[{timestamp}] Binance - {data['stream']}: received data")
                        else:
                            print(f"[{timestamp}] Binance - Response: {data}")
                    else:
                        if "arg" in data:
                            print(f"[{timestamp}] OKX - {data['arg']['channel']}: received data")
                        else:
                            print(f"[{timestamp}] OKX - Response: {data}")
                            
                except asyncio.TimeoutError:
                    continue
                except Exception as e:
                    print(f"Error receiving message: {e}")
                    break
                    
        except Exception as e:
            print(f"Error in listen loop: {e}")
    
    async def close(self):
        """关闭连接"""
        if self.websocket:
            await self.websocket.close()
            print(f"Closed connection to {self.uri}")

async def test_dual_subscription():
    """测试双重订阅"""
    print("=== Testing OKX and Binance Simultaneous Subscription ===\n")
    
    # 创建两个客户端
    binance_client = WebSocketClient("wss://localhost:8082", "binance")
    okx_client = WebSocketClient("wss://localhost:8084", "okx")
    
    try:
        # 连接到两个服务器
        binance_connected = await binance_client.connect()
        okx_connected = await okx_client.connect()
        
        if not binance_connected or not okx_connected:
            print("Failed to connect to one or both servers")
            return
        
        # 发送订阅请求
        await binance_client.subscribe_binance_format()
        await okx_client.subscribe_okx_format()
        
        # 等待一下让订阅生效
        await asyncio.sleep(2)
        
        # 同时监听两个连接
        print("\n=== Starting simultaneous listening ===")
        await asyncio.gather(
            binance_client.listen(30),
            okx_client.listen(30)
        )
        
        # 统计结果
        print(f"\n=== Results ===")
        print(f"Binance client received {len(binance_client.received_messages)} messages")
        print(f"OKX client received {len(okx_client.received_messages)} messages")
        
        # 显示一些示例消息
        if binance_client.received_messages:
            print(f"\nBinance sample message:")
            print(json.dumps(binance_client.received_messages[0], indent=2))
        
        if okx_client.received_messages:
            print(f"\nOKX sample message:")
            print(json.dumps(okx_client.received_messages[0], indent=2))
            
    except Exception as e:
        print(f"Test failed: {e}")
    finally:
        # 关闭连接
        await binance_client.close()
        await okx_client.close()

async def main():
    """主函数"""
    if len(sys.argv) > 1 and sys.argv[1] == "--help":
        print("Usage: python3 test_okx_binance.py")
        print("This script tests simultaneous subscription to OKX and Binance WebSocket servers")
        print("Make sure both servers are running on ports 8082 (Binance) and 8084 (OKX)")
        return
    
    await test_dual_subscription()

if __name__ == "__main__":
    asyncio.run(main())
