start_time = "2025-07-07T11:23:20Z"
end_time = "2025-07-07T11:25:00Z"
websocket_port = 8084
http_port = 8085
log_level = "info"
performance_target_us = 500

# HTTP服务器TLS配置
[http_tls]
# 启用TLS（HTTPS）
enabled = true

# 使用证书文件
[http_tls.cert_source]
type = "Files"
cert_path = "./certs/server.crt"
key_path = "./certs/server.key"

# WebSocket服务器TLS配置
[websocket_tls]
# 启用TLS（WSS）
enabled = true

# 使用证书文件
[websocket_tls.cert_source]
type = "Files"
cert_path = "./certs/server.crt"
key_path = "./certs/server.key"

[[data_paths]]
root = "./data"
quotes = "./data/quotes"
trades = "./data/trades"

# 按数据源区分的路径配置
[data_paths.sources.okx]
root = "./data/okex-swap/"
quotes = "./data/okex-swap/quotes"
trades = "./data/okex-swap/trades"

[data_paths]
root = "./data"
quotes = "./data/quotes"
trades = "./data/trades"

[account]
account_type = "Futures"
initial_balance = 10000.0
max_leverage = 10.0
margin_mode = "Cross"
maker_fee_rate = 0.0002
taker_fee_rate = 0.0004
funding_rate = 0.0001
supported_symbols = ["BTC-USDT", "ETH-USDT"]

# 数据回放配置
[playback]
# 回放速率（每秒处理的数据条数）
# 0 表示无限制，尽可能快地处理
rate_per_second = 1
# 是否启用回放速率控制
enabled = true
# 批处理大小（一次处理多少条数据）
batch_size = 10
