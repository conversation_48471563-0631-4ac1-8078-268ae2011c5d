#!/bin/bash

echo "🚀 开始测试下单和余额功能"
echo "=" 

echo ""
echo "💰 测试初始余额..."
echo "GET /fapi/v2/balance"
curl -k -s https://127.0.0.1:8083/fapi/v2/balance | jq '.'

echo ""
echo "📊 测试账户信息..."
echo "GET /fapi/v2/account"
curl -k -s https://127.0.0.1:8083/fapi/v2/account | jq '.'

echo ""
echo "📝 现在需要通过WebSocket下单来测试订单更新..."
echo "请在另一个终端运行WebSocket客户端来下单"

echo ""
echo "💡 建议的测试步骤："
echo "1. 启动WebSocket客户端订阅test_listen_key和btcusdt@bookTicker"
echo "2. 通过WebSocket发送下单消息"
echo "3. 观察订单更新推送"
echo "4. 再次查询余额API"

echo ""
echo "🔧 WebSocket客户端命令示例："
echo "cargo run --bin websocket_client -- -m interactive"
