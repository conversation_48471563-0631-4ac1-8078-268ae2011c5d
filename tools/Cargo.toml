[package]
name = "backtest-tools"
version = "0.1.0"
edition = "2021"

[[bin]]
name = "http_client"
path = "http_client.rs"

[[bin]]
name = "websocket_client"
path = "websocket_client.rs"

[[bin]]
name = "simple_order_test"
path = "simple_order_test.rs"

[[bin]]
name = "position_test"
path = "position_test.rs"

[[bin]]
name = "pnl_test"
path = "pnl_test.rs"

[[bin]]
name = "bugfix_test"
path = "bugfix_test.rs"

[dependencies]
tokio = { version = "1.0", features = ["full"] }
reqwest = { version = "0.11", features = ["json"], default-features = false }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
clap = { version = "4.0", features = ["derive"] }
tokio-tungstenite = { version = "0.20", features = ["rustls-tls-native-roots"] }
futures = "0.3"
futures-util = "0.3"
chrono = { version = "0.4", features = ["serde"] }
