#!/bin/bash

echo "🚀 开始订单执行测试"
echo "=" 
echo ""

# 测试函数
test_api() {
    local endpoint=$1
    local description=$2
    echo "📊 测试 $description..."
    echo "GET $endpoint"
    curl -k -s "https://127.0.0.1:8083$endpoint" | jq '.' 2>/dev/null || curl -k -s "https://127.0.0.1:8083$endpoint"
    echo ""
}

# 1. 测试初始状态
echo "💰 测试初始账户状态"
echo "-" 
test_api "/fapi/v2/balance" "余额信息"
test_api "/fapi/v2/account" "账户信息"

echo ""
echo "📝 现在需要通过WebSocket下单来测试订单执行..."
echo ""
echo "💡 测试步骤说明："
echo "1. 当前服务已启动，数据流正在运行"
echo "2. 初始余额: 10000 USDT"
echo "3. 需要通过WebSocket客户端下单测试"
echo "4. 可以使用现有的Python脚本或WebSocket工具"
echo ""

echo "🔧 可用的测试方法："
echo "方法1: 使用现有的Python测试脚本 (需要安装websockets库)"
echo "  pip3 install websockets requests"
echo "  python3 quick_order_test.py"
echo ""
echo "方法2: 使用WebSocket客户端工具"
echo "  连接: wss://127.0.0.1:8082"
echo "  订阅: {\"method\":\"SUBSCRIBE\",\"params\":[\"test_listen_key\"],\"id\":1}"
echo "  下单: {\"id\":123,\"method\":\"order.place\",\"params\":{\"symbol\":\"BTCUSDT\",\"side\":\"BUY\",\"type\":\"MARKET\",\"quantity\":\"0.001\"}}"
echo ""
echo "方法3: 使用Rust WebSocket客户端"
echo "  cargo run --bin websocket_client -- -m interactive"
echo ""

echo "📋 预期测试验证要点："
echo "1. 订单是否按BBO价格成交"
echo "2. 手续费计算是否正确 (0.1%)"
echo "3. 持仓数量和平均价格是否正确"
echo "4. 已实现/未实现盈亏计算是否正确"
echo "5. 保证金冻结机制是否正常"
echo ""

echo "🎯 建议的测试序列："
echo "1. 买入 0.001 BTC (开多仓)"
echo "2. 卖出 0.0005 BTC (减多仓)"  
echo "3. 买入 0.002 BTC (加多仓)"
echo "4. 卖出 0.0015 BTC (减多仓)"
echo ""

echo "📊 测试后验证："
echo "- 检查余额变化 (主要是手续费扣除)"
echo "- 检查持仓状态"
echo "- 检查未实现盈亏"
echo "- 验证订单执行价格是否合理"
echo ""

echo "✅ 准备工作完成，可以开始下单测试"
