# 订单执行测试指南

## 概述

本指南提供了全面的订单执行测试方案，用于验证回测框架的订单执行、手续费计算、持仓管理和盈亏计算功能。

## 测试脚本

### 1. 快速测试脚本 (`quick_order_test.py`)

**用途**: 快速验证基本订单执行功能

**功能**:
- 连接WebSocket和HTTP API
- 执行4个测试订单（买入、卖出、买入、卖出）
- 实时显示账户状态变化
- 验证订单执行和手续费计算

**使用方法**:
```bash
# 运行完整快速测试
python3 quick_order_test.py

# 运行单个订单测试
python3 quick_order_test.py single
```

### 2. 全面测试脚本 (`comprehensive_order_test.py`)

**用途**: 深度测试订单执行和账户管理

**功能**:
- 详细的订单执行分析
- 手续费计算验证
- 持仓管理测试
- 盈亏计算验证
- 完整的测试报告

**使用方法**:
```bash
# 运行全面测试
python3 comprehensive_order_test.py

# 运行持仓管理测试
python3 comprehensive_order_test.py position
```

## 测试前准备

### 1. 启动回测框架

```bash
# 在第一个终端启动回测框架
cargo run --bin backtest -- --config example_config.toml
```

### 2. 确认服务状态

```bash
# 检查HTTP API
curl -k https://127.0.0.1:8083/fapi/v2/balance

# 检查账户信息
curl -k https://127.0.0.1:8083/fapi/v2/account
```

## 测试验证要点

### 1. BBO价格成交验证

**验证内容**:
- 市价单是否按当前BBO价格成交
- 成交价格是否合理（接近市场价格）

**预期结果**:
- 买单成交价格接近ask价格
- 卖单成交价格接近bid价格
- 价格在合理范围内

### 2. 手续费计算验证

**验证内容**:
- 手续费率是否为配置的0.1% (0.001)
- 手续费计算公式: `成交量 × 成交价格 × 0.001`
- 手续费是否正确从账户余额中扣除

**预期结果**:
- 手续费计算准确
- 账户余额正确减少

### 3. 持仓管理验证

**验证内容**:
- 开仓: 新建持仓，数量和价格正确
- 加仓: 持仓数量增加，平均价格重新计算
- 减仓: 持仓数量减少，平均价格不变
- 平仓: 持仓清零，计算已实现盈亏

**预期结果**:
- 持仓数量计算正确
- 平均价格计算正确
- 保证金冻结机制正常

### 4. 盈亏计算验证

**验证内容**:
- 未实现盈亏: 基于当前价格和持仓计算
- 已实现盈亏: 平仓时计算价差收益
- 盈亏计算公式正确

**预期结果**:
- 未实现盈亏实时更新
- 已实现盈亏在平仓时正确计算

## 测试场景

### 场景1: 基本订单执行

```
1. 买入 0.001 BTC (开多仓)
2. 卖出 0.0005 BTC (减多仓)
3. 买入 0.002 BTC (加多仓)
4. 卖出 0.0015 BTC (减多仓)
```

### 场景2: 持仓变化测试

```
1. 买入 0.001 BTC (开多仓)
2. 买入 0.001 BTC (加多仓)
3. 卖出 0.0005 BTC (减多仓)
4. 卖出 0.002 BTC (平多仓+开空仓)
5. 买入 0.0015 BTC (平空仓)
```

## 预期测试结果

### 正常情况下的预期结果

1. **余额变化**: 主要是手续费扣除，期货交易不直接扣除本金
2. **可用余额**: 根据保证金冻结情况变化
3. **手续费**: 每笔交易约为 `成交金额 × 0.001`
4. **持仓**: 正确反映开仓、加仓、减仓、平仓操作
5. **盈亏**: 未实现盈亏随价格变化，已实现盈亏在平仓时计算

### 异常情况处理

1. **连接失败**: 检查服务是否启动
2. **订单失败**: 检查余额是否充足
3. **数据异常**: 检查配置文件和数据源

## 故障排除

### 常见问题

1. **WebSocket连接失败**
   - 检查端口8082是否开放
   - 确认TLS证书配置正确

2. **HTTP API访问失败**
   - 检查端口8083是否开放
   - 确认HTTPS证书配置正确

3. **订单执行失败**
   - 检查账户余额是否充足
   - 确认保证金计算正确

4. **数据不更新**
   - 检查数据源配置
   - 确认BBO数据流正常

### 调试建议

1. 查看服务器日志输出
2. 使用单个订单测试模式
3. 检查配置文件设置
4. 验证网络连接状态

## 测试报告模板

测试完成后，应记录以下信息：

```
测试时间: [时间戳]
测试类型: [快速测试/全面测试/持仓测试]
测试结果: [成功/失败]

账户变化:
- 初始余额: [金额] USDT
- 最终余额: [金额] USDT
- 余额变化: [金额] USDT
- 总手续费: [金额] USDT

订单执行:
- 总订单数: [数量]
- 成功订单: [数量]
- 失败订单: [数量]

持仓状态:
- 最终持仓: [详情]
- 未实现盈亏: [金额] USDT

验证结果:
- BBO价格成交: [✅/❌]
- 手续费计算: [✅/❌]
- 持仓管理: [✅/❌]
- 盈亏计算: [✅/❌]

问题记录:
[记录发现的问题和异常]
```
