#!/usr/bin/env python3
"""
测试下单和WebSocket订单更新的脚本
"""

import asyncio
import websockets
import json
import ssl
import requests
import time
from urllib3.exceptions import InsecureRequestWarning

# 禁用SSL警告
requests.packages.urllib3.disable_warnings(InsecureRequestWarning)

# 配置
WS_URL = "wss://127.0.0.1:8082"
HTTP_BASE_URL = "https://127.0.0.1:8083"

async def test_websocket_order_updates():
    """测试WebSocket订单更新"""
    print("🔗 连接到WebSocket服务器...")

    # 创建SSL上下文，忽略证书验证
    ssl_context = ssl.create_default_context()
    ssl_context.check_hostname = False
    ssl_context.verify_mode = ssl.CERT_NONE

    try:
        async with websockets.connect(WS_URL, ssl=ssl_context) as websocket:
            print("✅ WebSocket连接成功")

            # 订阅test_listen_key（订单更新）
            subscribe_msg = {
                "method": "SUBSCRIBE",
                "params": ["test_listen_key"],
                "id": 1
            }
            await websocket.send(json.dumps(subscribe_msg))
            print("📤 发送订阅消息:", json.dumps(subscribe_msg, indent=2))

            # 等待订阅确认
            response = await websocket.recv()
            print("📥 订阅响应:", response)

            # 订阅BBO数据
            bbo_subscribe_msg = {
                "method": "SUBSCRIBE",
                "params": ["btcusdt@bookTicker"],
                "id": 2
            }
            await websocket.send(json.dumps(bbo_subscribe_msg))
            print("📤 发送BBO订阅消息:", json.dumps(bbo_subscribe_msg, indent=2))

            # 等待BBO订阅确认
            response = await websocket.recv()
            print("📥 BBO订阅响应:", response)

            print("🎧 开始监听消息...")

            # 监听消息
            message_count = 0
            while message_count < 20:  # 监听20条消息
                try:
                    message = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                    message_count += 1

                    try:
                        data = json.loads(message)
                        if 'stream' in data and data['stream'] == 'test_listen_key':
                            print(f"📥 订单更新消息 #{message_count}:")
                            print(json.dumps(data, indent=2))
                        elif 'stream' in data and 'bookTicker' in data['stream']:
                            print(f"📥 BBO消息 #{message_count}: {data['stream']}")
                        else:
                            print(f"📥 其他消息 #{message_count}: {message[:100]}...")
                    except json.JSONDecodeError:
                        print(f"📥 非JSON消息 #{message_count}: {message[:100]}...")

                except asyncio.TimeoutError:
                    print("⏰ 等待消息超时")
                    break

    except Exception as e:
        print(f"❌ WebSocket连接错误: {e}")

def test_balance_api():
    """测试余额API"""
    print("\n💰 测试余额API...")

    try:
        response = requests.get(f"{HTTP_BASE_URL}/fapi/v2/balance", verify=False)
        if response.status_code == 200:
            balance_data = response.json()
            print("✅ 余额API响应:")
            print(json.dumps(balance_data, indent=2))
            return balance_data
        else:
            print(f"❌ 余额API错误: {response.status_code} - {response.text}")
            return None
    except Exception as e:
        print(f"❌ 余额API请求错误: {e}")
        return None

async def place_test_order_via_websocket():
    """通过WebSocket下测试订单"""
    print("\n📝 通过WebSocket下测试订单...")

    # 创建SSL上下文，忽略证书验证
    ssl_context = ssl.create_default_context()
    ssl_context.check_hostname = False
    ssl_context.verify_mode = ssl.CERT_NONE

    try:
        async with websockets.connect(WS_URL, ssl=ssl_context) as websocket:
            print("✅ WebSocket连接成功")

            # 订阅test_listen_key（订单更新）
            subscribe_msg = {
                "method": "SUBSCRIBE",
                "params": ["test_listen_key"],
                "id": 1
            }
            await websocket.send(json.dumps(subscribe_msg))
            print("📤 发送订阅消息")

            # 等待订阅确认
            response = await websocket.recv()
            print("📥 订阅响应:", response)

            # 发送下单消息
            order_msg = {
                "id": 123,
                "method": "order.place",
                "params": {
                    "symbol": "BTCUSDT",
                    "side": "BUY",
                    "type": "MARKET",
                    "quantity": "0.001",
                    "newClientOrderId": "test_order_001"
                }
            }

            print("📤 发送下单消息:", json.dumps(order_msg, indent=2))
            await websocket.send(json.dumps(order_msg))

            # 监听订单更新
            print("🎧 监听订单更新...")
            message_count = 0
            while message_count < 10:  # 监听10条消息
                try:
                    message = await asyncio.wait_for(websocket.recv(), timeout=3.0)
                    message_count += 1

                    try:
                        data = json.loads(message)
                        if 'stream' in data and data['stream'] == 'test_listen_key':
                            print(f"📥 订单更新消息 #{message_count}:")
                            print(json.dumps(data, indent=2))
                        elif 'result' in data or 'error' in data:
                            print(f"📥 下单响应 #{message_count}:")
                            print(json.dumps(data, indent=2))
                        else:
                            print(f"📥 其他消息 #{message_count}: {message[:100]}...")
                    except json.JSONDecodeError:
                        print(f"📥 非JSON消息 #{message_count}: {message[:100]}...")

                except asyncio.TimeoutError:
                    print("⏰ 等待消息超时")
                    break

    except Exception as e:
        print(f"❌ WebSocket下单错误: {e}")

async def main():
    """主函数"""
    print("🚀 开始测试下单和订单更新功能")
    print("=" * 50)

    # 1. 测试余额API
    initial_balance = test_balance_api()

    # 2. 通过WebSocket下单并监听订单更新
    print("\n📝 测试下单功能...")
    await place_test_order_via_websocket()

    # 3. 再次检查余额
    print("\n💰 再次检查余额...")
    final_balance = test_balance_api()

    # 4. 比较余额变化
    if initial_balance and final_balance:
        print("\n📊 余额变化分析:")
        initial_usdt = float(initial_balance[0]['balance'])
        final_usdt = float(final_balance[0]['balance'])
        print(f"初始余额: {initial_usdt} USDT")
        print(f"最终余额: {final_usdt} USDT")
        print(f"余额变化: {final_usdt - initial_usdt} USDT")

    print("\n✅ 测试完成")

if __name__ == "__main__":
    asyncio.run(main())
