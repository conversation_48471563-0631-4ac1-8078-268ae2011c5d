#!/usr/bin/env python3
"""
快速订单测试脚本
用于快速验证订单执行、手续费和持仓计算
"""

import asyncio
import websockets
import json
import ssl
import requests
import time
from urllib3.exceptions import InsecureRequestWarning

# 禁用SSL警告
requests.packages.urllib3.disable_warnings(InsecureRequestWarning)

# 配置
WS_URL = "wss://127.0.0.1:8082"
HTTP_BASE_URL = "https://127.0.0.1:8083"

def get_account_info():
    """获取账户信息"""
    try:
        response = requests.get(f"{HTTP_BASE_URL}/fapi/v2/account", verify=False)
        if response.status_code == 200:
            return response.json()
        else:
            print(f"❌ 获取账户信息失败: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ 获取账户信息异常: {e}")
        return None

def get_balance_info():
    """获取余额信息"""
    try:
        response = requests.get(f"{HTTP_BASE_URL}/fapi/v2/balance", verify=False)
        if response.status_code == 200:
            return response.json()
        else:
            print(f"❌ 获取余额信息失败: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ 获取余额信息异常: {e}")
        return None

def print_account_summary(label=""):
    """打印账户摘要"""
    print(f"\n📊 账户状态 [{label}]")
    print("-" * 40)
    
    account_info = get_account_info()
    balance_info = get_balance_info()
    
    if balance_info:
        balance = float(balance_info[0]['balance'])
        print(f"💰 钱包余额: {balance:.6f} USDT")
    
    if account_info:
        available = float(account_info.get('availableBalance', 0))
        unrealized_pnl = float(account_info.get('totalUnrealizedProfit', 0))
        
        print(f"💳 可用余额: {available:.6f} USDT")
        print(f"📈 未实现盈亏: {unrealized_pnl:.6f} USDT")
        
        # 打印持仓信息
        positions = account_info.get('positions', [])
        active_positions = [pos for pos in positions if float(pos.get('positionAmt', 0)) != 0]
        
        if active_positions:
            print(f"📍 活跃持仓:")
            for pos in active_positions:
                symbol = pos['symbol']
                quantity = float(pos['positionAmt'])
                entry_price = float(pos['entryPrice'])
                unrealized = float(pos['unrealizedProfit'])
                print(f"   {symbol}: {quantity:+.6f} @ {entry_price:.2f} (盈亏: {unrealized:+.6f})")
        else:
            print(f"📍 无活跃持仓")

async def place_order_and_wait(websocket, symbol, side, order_type, quantity, client_order_id):
    """下单并等待结果"""
    order_msg = {
        "id": int(time.time() * 1000),
        "method": "order.place",
        "params": {
            "symbol": symbol,
            "side": side,
            "type": order_type,
            "quantity": quantity,
            "newClientOrderId": client_order_id
        }
    }
    
    print(f"📤 下单: {symbol} {side} {order_type} {quantity}")
    await websocket.send(json.dumps(order_msg))
    
    # 等待订单确认
    response = await websocket.recv()
    print(f"📥 订单响应: {response}")
    
    # 等待订单更新
    print("⏳ 等待订单执行...")
    start_time = time.time()
    
    while time.time() - start_time < 5:
        try:
            message = await asyncio.wait_for(websocket.recv(), timeout=1.0)
            data = json.loads(message)
            
            if 'stream' in data and data['stream'] == 'test_listen_key':
                stream_data = data['data']
                if stream_data.get('e') == 'ORDER_TRADE_UPDATE':
                    order_data = stream_data.get('o', {})
                    
                    print(f"📋 订单更新:")
                    print(f"   订单ID: {order_data.get('i', '')}")
                    print(f"   状态: {order_data.get('X', '')}")
                    print(f"   成交量: {order_data.get('z', '0')}")
                    print(f"   平均价: {order_data.get('ap', '0')}")
                    print(f"   手续费: {order_data.get('n', '0')}")
                    
                    if order_data.get('X') == 'FILLED':
                        print("✅ 订单已完全成交")
                        return True
                        
        except asyncio.TimeoutError:
            continue
        except Exception as e:
            print(f"❌ 处理消息失败: {e}")
    
    print("⚠️ 等待订单执行超时")
    return False

async def run_quick_test():
    """运行快速测试"""
    print("🚀 开始快速订单测试")
    print("=" * 50)
    
    # 创建SSL上下文
    ssl_context = ssl.create_default_context()
    ssl_context.check_hostname = False
    ssl_context.verify_mode = ssl.CERT_NONE
    
    try:
        async with websockets.connect(WS_URL, ssl=ssl_context) as websocket:
            print("✅ WebSocket连接成功")
            
            # 订阅订单更新
            subscribe_msg = {
                "method": "SUBSCRIBE",
                "params": ["test_listen_key"],
                "id": 1
            }
            await websocket.send(json.dumps(subscribe_msg))
            response = await websocket.recv()
            print(f"📥 订阅响应: {response}")
            
            # 订阅BBO数据
            bbo_subscribe_msg = {
                "method": "SUBSCRIBE",
                "params": ["btcusdt@bookTicker"],
                "id": 2
            }
            await websocket.send(json.dumps(bbo_subscribe_msg))
            response = await websocket.recv()
            print(f"📥 BBO订阅响应: {response}")
            
            # 初始账户状态
            print_account_summary("初始状态")
            
            # 等待BBO数据
            print("\n⏳ 等待BBO数据...")
            await asyncio.sleep(2)
            
            # 测试订单序列
            test_orders = [
                ("BTCUSDT", "BUY", "MARKET", "0.001", "quick_test_1"),
                ("BTCUSDT", "SELL", "MARKET", "0.0005", "quick_test_2"),
                ("BTCUSDT", "BUY", "MARKET", "0.001", "quick_test_3"),
                ("BTCUSDT", "SELL", "MARKET", "0.001", "quick_test_4"),
            ]
            
            for i, (symbol, side, order_type, quantity, client_id) in enumerate(test_orders):
                print(f"\n📝 执行测试订单 {i+1}/{len(test_orders)}")
                
                # 下单前状态
                print_account_summary(f"订单{i+1}前")
                
                # 下单
                success = await place_order_and_wait(websocket, symbol, side, order_type, quantity, client_id)
                
                if success:
                    # 下单后状态
                    print_account_summary(f"订单{i+1}后")
                else:
                    print("❌ 订单执行失败")
                
                # 等待一下再执行下一个订单
                await asyncio.sleep(1)
            
            # 最终状态
            print_account_summary("最终状态")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
    
    print("\n✅ 快速测试完成")

async def run_single_order_test():
    """运行单个订单测试"""
    print("🚀 开始单个订单测试")
    print("=" * 50)
    
    ssl_context = ssl.create_default_context()
    ssl_context.check_hostname = False
    ssl_context.verify_mode = ssl.CERT_NONE
    
    try:
        async with websockets.connect(WS_URL, ssl=ssl_context) as websocket:
            print("✅ WebSocket连接成功")
            
            # 订阅
            subscribe_msg = {
                "method": "SUBSCRIBE",
                "params": ["test_listen_key"],
                "id": 1
            }
            await websocket.send(json.dumps(subscribe_msg))
            await websocket.recv()
            
            # 初始状态
            print_account_summary("下单前")
            
            # 下单
            await place_order_and_wait(websocket, "BTCUSDT", "BUY", "MARKET", "0.001", "single_test")
            
            # 最终状态
            print_account_summary("下单后")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
    
    print("\n✅ 单个订单测试完成")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "single":
        asyncio.run(run_single_order_test())
    else:
        asyncio.run(run_quick_test())
